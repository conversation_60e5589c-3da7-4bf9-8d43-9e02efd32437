# 飞书多维表格图片获取接口使用指南

## 🚀 功能概述

本接口提供了从飞书多维表格中获取图片的完整解决方案，支持：

✅ **服务器端自动管理Token** - 无需客户端传递token，自动处理过期重试
✅ **获取多维表格记录** - 支持分页、筛选、排序
✅ **自动识别图片字段** - 智能识别附件类型的图片字段
✅ **批量下载图片** - 支持将图片下载到本地服务器
✅ **灵活的查询条件** - 支持指定字段、记录ID、筛选条件
✅ **详细的响应信息** - 提供下载状态、文件信息、错误详情
✅ **Token过期自动重试** - 检测到token过期时自动刷新并重试

## 📋 前置准备

### 1. 飞书应用配置

在 `src/main/resources/application.yml` 中配置飞书应用信息：

```yaml
# 飞书API配置
feishu:
  api:
    base-url: https://open.feishu.cn
    app-id: cli_xxxxxxxxxx          # 替换为你的飞书应用ID
    app-secret: xxxxxxxxxx          # 替换为你的飞书应用密钥
    timeout: 30000
    token-cache-time: 7000
    connect-timeout: 10000
    read-timeout: 30000
```

### 2. 获取必要参数

- **app_token**: 多维表格的唯一标识符
  - 从多维表格URL中获取：`https://xxx.feishu.cn/base/bascnCMII2ORej2RItqpZZUNMIe`
  - `bascnCMII2ORej2RItqpZZUNMIe` 就是 app_token

- **table_id**: 数据表的唯一标识符
  - 从数据表URL中获取：`https://xxx.feishu.cn/base/bascnCMII2ORej2RItqpZZUNMIe?table=tblsRc9GRRXKqhvW`
  - `tblsRc9GRRXKqhvW` 就是 table_id

### 3. 应用权限配置

确保飞书应用具有以下权限：
- `bitable:app` - 获取多维表格信息
- `bitable:app:readonly` - 读取多维表格数据

## 🔐 Token自动管理机制

### Token管理特性

✅ **自动获取Token** - 服务启动时自动获取飞书应用访问令牌
✅ **智能缓存** - Token缓存约2小时，避免频繁请求
✅ **过期检测** - 自动检测Token过期（错误码99991663、99991664等）
✅ **自动重试** - Token过期时自动刷新并重试原始请求
✅ **并发安全** - 多线程环境下的Token管理安全

### Token过期处理流程

1. **检测过期**: 监控API响应中的Token相关错误
2. **强制刷新**: 清除缓存，重新获取新Token
3. **自动重试**: 使用新Token重新执行原始请求
4. **错误处理**: 如果重试仍失败，返回详细错误信息

### 客户端无需关心Token

- ❌ 不需要传递Token参数
- ❌ 不需要处理Token过期
- ❌ 不需要管理Token缓存
- ✅ 只需要传递业务参数（appToken、tableId等）

## 🔧 API接口说明

### 🌟 推荐接口：简化的获取多维表格图片

**接口地址**: `POST /api/feishu/bitable/images/simple`

**特点**:
- ✅ 服务器自动管理token，无需客户端传递
- ✅ Token过期自动重试，提高成功率
- ✅ 简化的请求参数，易于使用
- ✅ 完整的错误处理和日志记录

**请求参数**:
```json
{
  "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "downloadToLocal": true,
  "pageSize": 20,
  "includeImageDetails": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 3,
    "totalImages": 8,
    "successfulDownloads": 7,
    "failedDownloads": 1,
    "hasMore": false,
    "processingTime": 2500,
    "records": [
      {
        "recordId": "recqwIwhc6",
        "imageCount": 3,
        "successCount": 3,
        "failureCount": 0,
        "imageFields": {
          "图片": [
            {
              "fileToken": "boxcnxe8OIukXXXXXXXXXXXXXX",
              "originalName": "product.jpg",
              "fileType": "image/jpeg",
              "fileSize": 1024000,
              "originalUrl": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
              "localAccessUrl": "http://localhost:8080/uploads/2025/07/22/20250722_143022_abc12345.jpg",
              "downloadStatus": "SUCCESS",
              "downloadTime": "2025-07-22 14:30:22"
            }
          ]
        }
      }
    ]
  }
}
```

### 1. 获取多维表格记录

**接口地址**: `POST /api/feishu/bitable/records`

**请求参数**:
```json
{
  "appToken": "bascnCMII2ORej2RItqpZZUNMIe",
  "tableId": "tblsRc9GRRXKqhvW",
  "pageSize": 10,
  "viewId": "vewTpR1urY",
  "fieldNames": ["字段1", "字段2"],
  "filter": "CurrentValue.[字段名] = \"值\""
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "hasMore": false,
    "pageToken": "",
    "total": 5,
    "items": [
      {
        "recordId": "recqwIwhc6",
        "fields": {
          "字段名": "字段值",
          "图片字段": [
            {
              "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
              "name": "image.jpg",
              "type": "image/jpeg",
              "size": 1024000,
              "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
              "tmp_url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx"
            }
          ]
        }
      }
    ]
  }
}
```

### 2. 获取多维表格图片

**接口地址**: `POST /api/feishu/bitable/images`

**请求参数**:
```json
{
  "appToken": "bascnCMII2ORej2RItqpZZUNMIe",
  "tableId": "tblsRc9GRRXKqhvW",
  "downloadToLocal": true,
  "imageFields": ["图片", "头像", "封面"],
  "pageSize": 10,
  "includeImageDetails": true,
  "filter": "CurrentValue.[状态] = \"已发布\""
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 3,
    "totalImages": 8,
    "successfulDownloads": 7,
    "failedDownloads": 1,
    "hasMore": false,
    "processingTime": 2500,
    "records": [
      {
        "recordId": "recqwIwhc6",
        "imageCount": 3,
        "successCount": 3,
        "failureCount": 0,
        "imageFields": {
          "图片": [
            {
              "fileToken": "boxcnxe8OIukXXXXXXXXXXXXXX",
              "originalName": "product.jpg",
              "fileType": "image/jpeg",
              "fileSize": 1024000,
              "originalUrl": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
              "localAccessUrl": "http://localhost:8080/uploads/2025/07/22/20250722_143022_abc12345.jpg",
              "downloadStatus": "SUCCESS",
              "downloadTime": "2025-07-22 14:30:22"
            }
          ]
        }
      }
    ]
  }
}
```

### 3. 获取指定记录的图片

**接口地址**: `POST /api/feishu/bitable/images/record/{recordId}`

**路径参数**:
- `recordId`: 记录ID

**请求参数**:
```json
{
  "appToken": "bascnCMII2ORej2RItqpZZUNMIe",
  "tableId": "tblsRc9GRRXKqhvW",
  "downloadToLocal": true,
  "includeImageDetails": true
}
```

## 📝 使用示例

### 🌟 推荐：使用简化接口

#### 示例1: 获取所有图片并下载到本地

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 20
  }'
```

#### 示例2: 只获取指定字段的图片

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "imageFields": ["图片", "头像", "封面"],
    "downloadToLocal": false
  }'
```

#### 示例3: 带筛选条件的查询

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "filter": "CurrentValue.[状态] = \"已发布\"",
    "downloadToLocal": true
  }'
```

#### 示例4: 获取指定记录的图片

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "downloadToLocal": true
  }'
```

## 🔍 测试工具

### 1. 使用测试脚本

```bash
# 修改脚本中的参数
vim test-feishu-bitable.sh

# 运行测试
./test-feishu-bitable.sh
```

### 2. 使用Swagger文档

访问 http://localhost:8080/doc.html 查看交互式API文档

### 3. 健康检查

```bash
curl -X GET "http://localhost:8080/api/feishu/bitable/health"
```

## ⚠️ 注意事项

### 1. 权限配置
- 确保飞书应用有访问目标多维表格的权限
- 检查应用是否在对应的飞书组织中

### 2. 参数格式
- `app_token` 和 `table_id` 必须正确
- 筛选条件使用飞书公式语法
- 字段名称区分大小写

### 3. 图片下载
- 下载的图片保存在 `uploads/` 目录下
- 支持的图片格式：jpg, jpeg, png, gif, bmp, webp
- 最大文件大小：200MB

### 4. 性能考虑
- 建议设置合适的 `pageSize`（默认100，最大500）
- 大量图片下载时注意服务器存储空间
- 可以设置 `downloadToLocal: false` 只获取URL不下载

## 🐛 故障排除

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 99991663 | app_access_token 无效 | 检查应用配置，重新获取令牌 |
| 99991664 | 应用权限不足 | 在飞书开放平台添加相应权限 |
| 99991665 | app_token 无效 | 检查多维表格的 app_token |
| 99991666 | table_id 无效 | 检查数据表的 table_id |

### 调试步骤

1. **检查配置**: 确认 `application.yml` 中的飞书配置正确
2. **验证权限**: 在飞书开放平台检查应用权限
3. **测试连接**: 使用健康检查接口测试服务状态
4. **查看日志**: 检查 `logs/ziniao-ai-demo.log` 文件
5. **逐步测试**: 先测试获取记录，再测试图片下载

## 📞 技术支持

如有问题，请：
1. 查看项目 README.md
2. 检查 Swagger 文档
3. 查看日志文件
4. 联系技术支持
