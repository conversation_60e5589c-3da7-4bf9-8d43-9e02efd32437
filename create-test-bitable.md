# 创建测试用多维表格的完整指南

## 🎯 问题总结

你当前的链接是知识库链接，不是多维表格链接：
- **当前链接**: `https://lbit922efv.feishu.cn/wiki/Wc2WwTiksil7vVkE1hqcmCmmneb`
- **链接类型**: 知识库 (wiki)
- **需要的类型**: 多维表格 (bitable)

## 🛠️ 解决方案：创建测试用多维表格

### 步骤1: 创建新的多维表格

1. **打开飞书客户端或网页版**
2. **点击"+"创建新应用**
3. **选择"多维表格"**
4. **创建一个测试表格，包含以下字段**：
   - 文本字段：名称、描述
   - 图片字段：头像、产品图片、封面图片
   - 其他字段：状态、创建时间等

### 步骤2: 添加测试数据

1. **添加几行测试数据**
2. **在图片字段中上传一些测试图片**
3. **确保有不同类型的图片数据**

### 步骤3: 获取正确的链接

1. **在多维表格中，点击右上角的"分享"**
2. **复制链接，格式应该是**：
   ```
   https://xxx.feishu.cn/base/bascnCMII2ORej2RItqpZZUNMIe?table=tblsRc9GRRXKqhvW&view=vewTpR1urY
   ```
3. **注意链接中包含 `/base/` 而不是 `/wiki/`**

### 步骤4: 提取参数

从新的链接中提取：
- **app_token**: `/base/` 后面的部分
- **table_id**: `table=` 后面的部分  
- **view_id**: `view=` 后面的部分

## 🔧 配置飞书应用权限

### 必需权限

在飞书开放平台为你的应用添加以下权限：

1. **多维表格权限**：
   - `bitable:app` - 获取多维表格信息
   - `bitable:app:readonly` - 读取多维表格数据

2. **文件权限**（用于下载图片）：
   - `drive:drive:readonly` - 读取云文档文件

### 权限配置步骤

1. **登录飞书开放平台**: https://open.feishu.cn/
2. **进入应用管理**: 找到你的应用 `cli_a8fe3e73bd78d00d`
3. **权限管理**: 添加上述权限
4. **发布权限**: 保存并发布权限更改
5. **安装应用**: 确保应用安装到包含多维表格的组织

## 🧪 测试新配置

创建新的多维表格后，使用以下参数测试：

```json
{
  "appToken": "新的app_token",
  "tableId": "新的table_id", 
  "viewId": "新的view_id",
  "downloadToLocal": true,
  "pageSize": 10,
  "includeImageDetails": true
}
```

## 📝 完整的测试命令

```bash
# 替换为你的新参数
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "你的新app_token",
    "tableId": "你的新table_id",
    "viewId": "你的新view_id",
    "downloadToLocal": true,
    "pageSize": 5,
    "includeImageDetails": true
  }'
```

## ⚠️ 重要提醒

1. **确保使用多维表格链接，不是知识库链接**
2. **链接必须包含 `/base/` 而不是 `/wiki/`**
3. **应用必须有正确的权限配置**
4. **应用必须安装到多维表格所在的组织**

## 🔗 相关链接

- **飞书开放平台**: https://open.feishu.cn/
- **你的应用管理**: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d
- **多维表格API文档**: https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview
- **权限配置指南**: https://open.feishu.cn/document/server-docs/authentication-management/access-token/app_access_token_internal

## 📞 如果仍有问题

如果按照上述步骤操作后仍有问题，请提供：
1. 新的多维表格链接
2. 错误信息截图
3. 应用权限配置截图

这样我就能帮你进一步调试和解决问题。
