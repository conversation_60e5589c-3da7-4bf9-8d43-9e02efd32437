package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 飞书多维表格请求参数
 */
@ApiModel(description = "飞书多维表格请求参数")
public class FeishuBitableRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "bascnCMII2ORej2RItqpZZUNMIe")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tblsRc9GRRXKqhvW")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewTpR1urY")
    private String viewId;

    @ApiModelProperty(value = "分页标记，第一次请求不填，表示从头开始遍历", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "分页大小，最大值是 500", example = "10")
    private Integer pageSize = 100;

    @ApiModelProperty(value = "控制字段的返回格式", example = "user_id")
    private String userIdType = "user_id";

    @ApiModelProperty(value = "指定要返回的字段名或字段ID列表")
    private List<String> fieldNames;

    @ApiModelProperty(value = "筛选条件")
    private String filter;

    @ApiModelProperty(value = "排序条件")
    private List<SortCondition> sort;

    @ApiModelProperty(value = "是否自动识别数字类型的字段并进行格式化")
    private Boolean automaticFields;

    /**
     * 排序条件
     */
    @ApiModel(description = "排序条件")
    public static class SortCondition {
        @ApiModelProperty(value = "字段名", required = true)
        private String fieldName;

        @ApiModelProperty(value = "排序方向，ASC 或 DESC", required = true)
        private String desc;

        // Getters and Setters
        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUserIdType() {
        return userIdType;
    }

    public void setUserIdType(String userIdType) {
        this.userIdType = userIdType;
    }

    public List<String> getFieldNames() {
        return fieldNames;
    }

    public void setFieldNames(List<String> fieldNames) {
        this.fieldNames = fieldNames;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public List<SortCondition> getSort() {
        return sort;
    }

    public void setSort(List<SortCondition> sort) {
        this.sort = sort;
    }

    public Boolean getAutomaticFields() {
        return automaticFields;
    }

    public void setAutomaticFields(Boolean automaticFields) {
        this.automaticFields = automaticFields;
    }

    @Override
    public String toString() {
        return "FeishuBitableRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", pageToken='" + pageToken + '\'' +
                ", pageSize=" + pageSize +
                ", userIdType='" + userIdType + '\'' +
                ", fieldNames=" + fieldNames +
                ", filter='" + filter + '\'' +
                ", sort=" + sort +
                ", automaticFields=" + automaticFields +
                '}';
    }
}
