package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.List;

/**
 * 飞书多维表格特定图片字段处理请求参数
 * 专门用于处理 downImageUrl、downOriginUrl、modelImageUrl、modelMaskImageUrl、upperImageUrl、upperOriginUrl 这6个字段
 */
@ApiModel(description = "飞书多维表格特定图片字段处理请求参数")
public class FeishuSpecificImageFieldsRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "bascnCMII2ORej2RItqpZZUNMIe")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tblsRc9GRRXKqhvW")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "记录ID，如果指定则只处理该记录", example = "recqwIwhc6")
    private String recordId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewTpR1urY")
    private String viewId;

    @ApiModelProperty(value = "筛选条件，用于筛选要处理的记录")
    private String filter;

    @ApiModelProperty(value = "分页大小，最大值是 500", example = "10")
    private Integer pageSize = 100;

    @ApiModelProperty(value = "分页标记，第一次请求不填", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "图片下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

    @ApiModelProperty(value = "最大并发下载数", example = "5")
    private Integer maxConcurrentDownloads = 5;

    @ApiModelProperty(value = "是否将本地URL写回多维表格", example = "true")
    private Boolean updateBitableWithLocalUrl = true;

    /**
     * 获取要处理的特定图片字段列表
     * 根据实际的表格结构，包含带"附件"后缀的字段名
     */
    public List<String> getSpecificImageFields() {
        return Arrays.asList(
            "downImageUrl",
            "downOriginUrl",
            "modelImageUrl",
            "modelMaskImageUrl",
            "upperImageUrl",
            "upperOriginUrl",
            // 带"附件"后缀的字段名
            "downImageUrl附件",
            "downOriginUrl附件",
            "modelImageUrl附件",
            "modelMaskImageUrl附件",
            "upperImageUrl附件",
            "upperOriginUrl附件"
        );
    }

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Integer getDownloadTimeout() {
        return downloadTimeout;
    }

    public void setDownloadTimeout(Integer downloadTimeout) {
        this.downloadTimeout = downloadTimeout;
    }

    public Integer getMaxConcurrentDownloads() {
        return maxConcurrentDownloads;
    }

    public void setMaxConcurrentDownloads(Integer maxConcurrentDownloads) {
        this.maxConcurrentDownloads = maxConcurrentDownloads;
    }

    public Boolean getUpdateBitableWithLocalUrl() {
        return updateBitableWithLocalUrl;
    }

    public void setUpdateBitableWithLocalUrl(Boolean updateBitableWithLocalUrl) {
        this.updateBitableWithLocalUrl = updateBitableWithLocalUrl;
    }

    @Override
    public String toString() {
        return "FeishuSpecificImageFieldsRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", recordId='" + recordId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", filter='" + filter + '\'' +
                ", pageSize=" + pageSize +
                ", pageToken='" + pageToken + '\'' +
                ", downloadTimeout=" + downloadTimeout +
                ", maxConcurrentDownloads=" + maxConcurrentDownloads +
                ", updateBitableWithLocalUrl=" + updateBitableWithLocalUrl +
                '}';
    }
}
