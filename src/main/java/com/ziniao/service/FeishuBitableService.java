package com.ziniao.service;

import com.alibaba.fastjson.JSON;
import com.ziniao.config.FeishuConfig;
import com.ziniao.exception.TokenExpiredException;
import com.ziniao.model.feishu.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import javax.annotation.PostConstruct;

/**
 * 飞书多维表格服务
 * 负责获取多维表格数据和处理图片字段
 */
@Service
public class FeishuBitableService {

    private static final Logger logger = LoggerFactory.getLogger(FeishuBitableService.class);

    @Autowired
    private FeishuConfig feishuConfig;

    @Autowired
    private FeishuTokenService tokenService;

    @Value("${file.upload.server-base-url:http://localhost:8080}")
    private String serverBaseUrl;

    @Value("${image.proxy.enabled:true}")
    private boolean imageProxyEnabled;

    @Autowired(required = false)
    private ImageProxyService imageProxyService;

    // 线程池用于并发下载图片
    private final ExecutorService downloadExecutor = Executors.newFixedThreadPool(10);

    /**
     * 初始化方法，显示图片代理配置状态
     */
    @PostConstruct
    public void init() {
        logger.info("=== 飞书多维表格服务初始化 ===");
        logger.info("图片代理功能启用状态: {}", imageProxyEnabled);
        logger.info("ImageProxyService注入状态: {}", imageProxyService != null ? "已注入" : "未注入");
        logger.info("服务器基础URL: {}", serverBaseUrl);
        logger.info("=== 初始化完成 ===");
    }

    /**
     * 获取多维表格记录（自动管理token）
     *
     * @param request 请求参数
     * @return 表格记录响应
     * @throws Exception 获取失败时抛出异常
     */
    public FeishuBitableResponse getRecords(FeishuBitableRequest request) throws Exception {
        logger.info("获取飞书多维表格记录: {}", request);

        return executeWithTokenRetry(() -> {
            try {
                return getRecordsInternal(request, null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 更新多维表格记录（自动管理token）
     *
     * @param request 更新请求参数
     * @return 更新响应
     * @throws Exception 更新失败时抛出异常
     */
    public FeishuBitableUpdateResponse updateRecord(FeishuBitableUpdateRequest request) throws Exception {
        logger.info("更新飞书多维表格记录: {}", request);

        return executeWithTokenRetry(() -> {
            try {
                return updateRecordInternal(request);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 内部更新记录方法
     */
    private FeishuBitableUpdateResponse updateRecordInternal(FeishuBitableUpdateRequest request) throws Exception {
        // 获取访问令牌
        String accessToken = tokenService.getAppAccessToken();

        // 构建请求URL
        String url = buildUpdateRecordUrl(request);
        logger.info("更新记录请求URL: {}", url);

        // 创建HTTP连接
        URL urlObj = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        connection.setRequestMethod("PUT");
        connection.setRequestProperty("Authorization", "Bearer " + accessToken);
        connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
        connection.setConnectTimeout(feishuConfig.getConnectTimeout());
        connection.setReadTimeout(feishuConfig.getReadTimeout());
        connection.setDoOutput(true);

        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("fields", request.getFields());

            String requestBodyJson = JSON.toJSONString(requestBody);
            logger.debug("更新记录请求体: {}", requestBodyJson);

            // 发送请求体
            try (OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream(), StandardCharsets.UTF_8)) {
                writer.write(requestBodyJson);
                writer.flush();
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();

            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ?
                            connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            logger.info("更新记录响应状态码: {}", responseCode);
            logger.debug("更新记录响应内容: {}", response.toString());

            // 检查是否是token过期错误
            if (responseCode == 401 || isTokenExpiredError(response.toString())) {
                logger.warn("检测到token过期，抛出异常以触发重试");
                throw new TokenExpiredException("Token已过期");
            }

            if (responseCode != 200) {
                throw new Exception("更新飞书多维表格记录失败，状态码: " + responseCode + ", 响应: " + response.toString());
            }

            // 解析响应
            Map<String, Object> responseMap = JSON.parseObject(response.toString(), Map.class);

            if (!responseMap.containsKey("code") || (Integer) responseMap.get("code") != 0) {
                String errorMsg = responseMap.containsKey("msg") ? responseMap.get("msg").toString() : "未知错误";
                throw new Exception("更新飞书多维表格记录失败: " + errorMsg);
            }

            // 构建成功响应
            FeishuBitableUpdateResponse.Data responseData = new FeishuBitableUpdateResponse.Data();
            responseData.setRecordId(request.getRecordId());

            if (responseMap.containsKey("data")) {
                Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
                if (dataMap.containsKey("fields")) {
                    responseData.setFields((Map<String, Object>) dataMap.get("fields"));
                }
            }

            responseData.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            logger.info("成功更新飞书多维表格记录: {}", request.getRecordId());
            return FeishuBitableUpdateResponse.success(responseData);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 构建更新记录的URL
     */
    private String buildUpdateRecordUrl(FeishuBitableUpdateRequest request) throws Exception {
        StringBuilder url = new StringBuilder();
        url.append(feishuConfig.getBaseUrl())
           .append("/open-apis/bitable/v1/apps/")
           .append(request.getAppToken())
           .append("/tables/")
           .append(request.getTableId())
           .append("/records/")
           .append(request.getRecordId());

        // 添加查询参数
        if (request.getUserIdType() != null && !request.getUserIdType().trim().isEmpty()) {
            url.append("?user_id_type=").append(URLEncoder.encode(request.getUserIdType(), StandardCharsets.UTF_8.name()));
        }

        return url.toString();
    }

    /**
     * 内部获取记录方法
     */
    private FeishuBitableResponse getRecordsInternal(FeishuBitableRequest request, String recordId) throws Exception {
        // 获取访问令牌
        String accessToken = tokenService.getAppAccessToken();

        // 构建请求URL
        String url = buildRecordsUrl(request, recordId);
        logger.info("请求URL: {}", url);

        HttpURLConnection connection = null;
        try {
            // 创建连接
            URL urlObj = new URL(url);
            connection = (HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Authorization", "Bearer " + accessToken);
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setConnectTimeout(feishuConfig.getConnectTimeout());
            connection.setReadTimeout(feishuConfig.getReadTimeout());

            // 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();

            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ?
                            connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            logger.info("飞书API响应状态码: {}", responseCode);
            logger.debug("飞书API响应内容: {}", response.toString());

            // 检查是否是token过期错误
            if (responseCode == 401 || isTokenExpiredError(response.toString())) {
                logger.warn("检测到token过期，抛出异常以触发重试");
                throw new TokenExpiredException("Token已过期");
            }

            if (responseCode != 200) {
                throw new Exception("获取飞书多维表格记录失败，状态码: " + responseCode + ", 响应: " + response.toString());
            }

            // 解析响应
            FeishuBitableResponse bitableResponse;

            if (recordId != null && !recordId.trim().isEmpty()) {
                // 单个记录的响应格式处理
                bitableResponse = parseSingleRecordResponse(response.toString(), recordId);
            } else {
                // 批量记录的响应格式处理
                bitableResponse = JSON.parseObject(response.toString(), FeishuBitableResponse.class);
            }

            if (!bitableResponse.isSuccess()) {
                // 检查是否是token相关错误
                if (isTokenExpiredError(bitableResponse.getMsg())) {
                    throw new TokenExpiredException("Token已过期: " + bitableResponse.getMsg());
                }

                throw new Exception("获取飞书多维表格记录失败，错误码: " + bitableResponse.getCode() +
                        ", 错误信息: " + bitableResponse.getMsg());
            }

            logger.info("成功获取飞书多维表格记录，共 {} 条",
                    bitableResponse.getData() != null && bitableResponse.getData().getItems() != null ?
                    bitableResponse.getData().getItems().size() : 0);

            return bitableResponse;

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 获取多维表格中的图片并下载到本地（自动管理token）
     *
     * @param request 图片下载请求参数
     * @return 图片下载响应
     * @throws Exception 处理失败时抛出异常
     */
    public FeishuImageDownloadResponse downloadImages(FeishuImageDownloadRequest request) throws Exception {
        logger.info("开始处理飞书多维表格图片下载: {}", request);
        long startTime = System.currentTimeMillis();

        return executeWithTokenRetry(() -> {
            try {
                return downloadImagesInternal(request, startTime);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 内部图片下载方法
     */
    private FeishuImageDownloadResponse downloadImagesInternal(FeishuImageDownloadRequest request, long startTime) throws Exception {
        // 构建查询请求
        FeishuBitableRequest bitableRequest = buildBitableRequest(request);

        // 获取表格记录
        FeishuBitableResponse bitableResponse = getRecordsInternal(bitableRequest, request.getRecordId());

        if (!bitableResponse.isSuccess() || bitableResponse.getData() == null ||
            bitableResponse.getData().getItems() == null) {
            return FeishuImageDownloadResponse.error(400, "获取表格记录失败");
        }

        // 处理图片下载
        FeishuImageDownloadResponse.Data responseData = processImageDownloads(
                bitableResponse.getData().getItems(), request);

        // 设置分页信息
        responseData.setHasMore(bitableResponse.getData().isHasMore());
        responseData.setNextPageToken(bitableResponse.getData().getPageToken());
        responseData.setProcessingTime(System.currentTimeMillis() - startTime);

        logger.info("飞书多维表格图片下载处理完成，耗时: {} ms", responseData.getProcessingTime());

        return FeishuImageDownloadResponse.success(responseData);
    }

    /**
     * 解析单个记录的响应
     */
    private FeishuBitableResponse parseSingleRecordResponse(String responseStr, String recordId) {
        try {
            // 尝试解析为标准的批量响应格式
            FeishuBitableResponse batchResponse = JSON.parseObject(responseStr, FeishuBitableResponse.class);
            if (batchResponse.isSuccess() && batchResponse.getData() != null) {
                return batchResponse;
            }

            // 如果不是标准格式，尝试解析为单个记录格式
            Map<String, Object> responseMap = JSON.parseObject(responseStr, Map.class);

            if (responseMap.containsKey("code") && (Integer) responseMap.get("code") == 0) {
                // 创建标准的批量响应格式
                FeishuBitableResponse response = new FeishuBitableResponse();
                response.setCode(0);
                response.setMsg("success");

                FeishuBitableResponse.Data data = new FeishuBitableResponse.Data();
                data.setHasMore(false);
                data.setTotal(1);

                // 解析单个记录
                if (responseMap.containsKey("data")) {
                    Map<String, Object> recordData = (Map<String, Object>) responseMap.get("data");
                    FeishuBitableResponse.Record record = new FeishuBitableResponse.Record();
                    record.setRecordId(recordId);
                    record.setFields(recordData);

                    data.setItems(Arrays.asList(record));
                }

                response.setData(data);
                return response;
            }

            // 如果解析失败，返回错误响应
            FeishuBitableResponse errorResponse = new FeishuBitableResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("解析单个记录响应失败");
            return errorResponse;

        } catch (Exception e) {
            logger.error("解析单个记录响应异常: {}", e.getMessage(), e);
            FeishuBitableResponse errorResponse = new FeishuBitableResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("解析响应异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 构建记录查询URL
     */
    private String buildRecordsUrl(FeishuBitableRequest request, String recordId) throws Exception {
        StringBuilder url = new StringBuilder();
        url.append(feishuConfig.getBaseUrl())
           .append("/open-apis/bitable/v1/apps/")
           .append(request.getAppToken())
           .append("/tables/")
           .append(request.getTableId())
           .append("/records");

        // 如果指定了recordId，直接在URL路径中添加
        if (recordId != null && !recordId.trim().isEmpty()) {
            url.append("/").append(recordId);
            logger.info("构建单个记录获取URL，recordId: {}", recordId);

            // 对于单个记录获取，只需要基本参数
            List<String> params = new ArrayList<>();
            if (request.getUserIdType() != null && !request.getUserIdType().trim().isEmpty()) {
                params.add("user_id_type=" + URLEncoder.encode(request.getUserIdType(), StandardCharsets.UTF_8.name()));
            }

            if (!params.isEmpty()) {
                url.append("?").append(String.join("&", params));
            }

            return url.toString();
        }

        // 批量获取记录的查询参数
        List<String> params = new ArrayList<>();

        if (request.getViewId() != null && !request.getViewId().trim().isEmpty()) {
            params.add("view_id=" + URLEncoder.encode(request.getViewId(), StandardCharsets.UTF_8.name()));
        }

        if (request.getPageToken() != null && !request.getPageToken().trim().isEmpty()) {
            params.add("page_token=" + URLEncoder.encode(request.getPageToken(), StandardCharsets.UTF_8.name()));
        }

        if (request.getPageSize() != null) {
            params.add("page_size=" + request.getPageSize());
        }

        if (request.getUserIdType() != null && !request.getUserIdType().trim().isEmpty()) {
            params.add("user_id_type=" + URLEncoder.encode(request.getUserIdType(), StandardCharsets.UTF_8.name()));
        }

        if (request.getFieldNames() != null && !request.getFieldNames().isEmpty()) {
            String fieldNames = String.join(",", request.getFieldNames());
            params.add("field_names=" + URLEncoder.encode(fieldNames, StandardCharsets.UTF_8.name()));
        }

        if (request.getFilter() != null && !request.getFilter().trim().isEmpty()) {
            params.add("filter=" + URLEncoder.encode(request.getFilter(), StandardCharsets.UTF_8.name()));
        }

        if (!params.isEmpty()) {
            url.append("?").append(String.join("&", params));
        }

        return url.toString();
    }

    /**
     * 构建多维表格请求参数
     */
    private FeishuBitableRequest buildBitableRequest(FeishuImageDownloadRequest request) {
        FeishuBitableRequest bitableRequest = new FeishuBitableRequest();
        bitableRequest.setAppToken(request.getAppToken());
        bitableRequest.setTableId(request.getTableId());
        bitableRequest.setViewId(request.getViewId());

        // 正确处理 pageToken - 不要将 recordId 设置为 pageToken
        bitableRequest.setPageToken(request.getPageToken());

        bitableRequest.setPageSize(request.getPageSize());

        // 只有当 imageFields 不为空时才设置 fieldNames
        // 这样避免了字段名称在API请求中的编码问题
        if (request.getImageFields() != null && !request.getImageFields().isEmpty()) {
            bitableRequest.setFieldNames(request.getImageFields());
        }

        // 处理筛选条件 - 注意：recordId 不通过 filter 处理，而是通过 URL 路径
        bitableRequest.setFilter(request.getFilter());

        return bitableRequest;
    }

    /**
     * 处理图片下载
     */
    private FeishuImageDownloadResponse.Data processImageDownloads(
            List<FeishuBitableResponse.Record> records, 
            FeishuImageDownloadRequest request) {

        FeishuImageDownloadResponse.Data data = new FeishuImageDownloadResponse.Data();
        List<FeishuImageDownloadResponse.RecordImageInfo> recordInfos = new ArrayList<>();

        int totalImages = 0;
        int successfulDownloads = 0;
        int failedDownloads = 0;

        for (FeishuBitableResponse.Record record : records) {
            // 如果指定了记录ID，只处理该记录
            if (request.getRecordId() != null && !request.getRecordId().equals(record.getRecordId())) {
                continue;
            }

            FeishuImageDownloadResponse.RecordImageInfo recordInfo = processRecordImages(record, request);
            recordInfos.add(recordInfo);

            totalImages += recordInfo.getImageCount();
            successfulDownloads += recordInfo.getSuccessCount();
            failedDownloads += recordInfo.getFailureCount();
        }

        data.setTotalRecords(recordInfos.size());
        data.setTotalImages(totalImages);
        data.setSuccessfulDownloads(successfulDownloads);
        data.setFailedDownloads(failedDownloads);
        data.setRecords(recordInfos);

        return data;
    }

    /**
     * 处理单个记录的图片
     */
    private FeishuImageDownloadResponse.RecordImageInfo processRecordImages(
            FeishuBitableResponse.Record record,
            FeishuImageDownloadRequest request) {

        FeishuImageDownloadResponse.RecordImageInfo recordInfo = new FeishuImageDownloadResponse.RecordImageInfo();
        recordInfo.setRecordId(record.getRecordId());

        Map<String, List<FeishuImageDownloadResponse.ImageInfo>> imageFields = new HashMap<>();
        Map<String, Object> updatedFields = new HashMap<>(); // 用于存储需要更新的字段
        int imageCount = 0;
        int successCount = 0;
        int failureCount = 0;

        // 遍历记录的所有字段
        if (record.getFields() != null) {
            for (Map.Entry<String, Object> fieldEntry : record.getFields().entrySet()) {
                String fieldName = fieldEntry.getKey();
                Object fieldValue = fieldEntry.getValue();

                // 检查是否是图片字段
                if (isImageField(fieldName, fieldValue, request.getImageFields())) {
                    List<FeishuImageDownloadResponse.ImageInfo> images = processImageField(fieldValue, request);
                    imageFields.put(fieldName, images);

                    imageCount += images.size();
                    successCount += (int) images.stream().filter(img -> "SUCCESS".equals(img.getDownloadStatus())).count();
                    failureCount += (int) images.stream().filter(img -> "FAILED".equals(img.getDownloadStatus())).count();

                    // 如果有成功下载的图片，准备更新字段
                    if (images.stream().anyMatch(img -> "SUCCESS".equals(img.getDownloadStatus()))) {
                        logger.info("检测到成功下载的图片，准备构建更新字段: fieldName={}, successCount={}",
                                fieldName, images.stream().filter(img -> "SUCCESS".equals(img.getDownloadStatus())).count());

                        List<Map<String, Object>> updatedAttachments = new ArrayList<>();

                        // 重新构建附件列表，添加本地URL信息
                        if (fieldValue instanceof List) {
                            List<?> attachments = (List<?>) fieldValue;
                            for (int i = 0; i < attachments.size() && i < images.size(); i++) {
                                if (attachments.get(i) instanceof Map) {
                                    Map<String, Object> attachment = new HashMap<>((Map<String, Object>) attachments.get(i));
                                    FeishuImageDownloadResponse.ImageInfo imageInfo = images.get(i);

                                    // 如果下载成功，添加本地URL字段
                                    if ("SUCCESS".equals(imageInfo.getDownloadStatus()) && imageInfo.getLocalAccessUrl() != null) {
                                        attachment.put("local_url", imageInfo.getLocalAccessUrl());
                                        attachment.put("local_download_time", imageInfo.getDownloadTime());
                                        attachment.put("local_status", "downloaded");

                                        logger.info("为图片添加本地URL信息: name={}, localUrl={}",
                                                imageInfo.getOriginalName(), imageInfo.getLocalAccessUrl());
                                    }

                                    updatedAttachments.add(attachment);
                                }
                            }
                        }

                        updatedFields.put(fieldName, updatedAttachments);
                        logger.info("字段更新数据已准备: fieldName={}, attachmentCount={}", fieldName, updatedAttachments.size());
                    }
                }
            }
        }

        recordInfo.setImageFields(imageFields);
        recordInfo.setImageCount(imageCount);
        recordInfo.setSuccessCount(successCount);
        recordInfo.setFailureCount(failureCount);

        // 如果有需要更新的字段且启用了写回功能，更新多维表格记录
        logger.info("检查是否需要更新多维表格: recordId={}, hasUpdatedFields={}, shouldUpdate={}",
                record.getRecordId(), !updatedFields.isEmpty(), shouldUpdateBitableRecord(request));

        if (!updatedFields.isEmpty() && shouldUpdateBitableRecord(request)) {
            logger.info("开始自动写回本地URL到多维表格: recordId={}, fieldsToUpdate={}",
                    record.getRecordId(), updatedFields.keySet());
            updateBitableRecordWithLocalUrls(record.getRecordId(), updatedFields, request);
        } else if (!updatedFields.isEmpty()) {
            logger.info("有更新字段但写回功能被禁用: recordId={}, downloadToLocal={}, updateBitableWithLocalUrl={}",
                    record.getRecordId(), request.getDownloadToLocal(), request.getUpdateBitableWithLocalUrl());
        } else {
            logger.info("没有需要更新的字段: recordId={}", record.getRecordId());
        }

        return recordInfo;
    }

    /**
     * 判断是否应该更新多维表格记录
     */
    private boolean shouldUpdateBitableRecord(FeishuImageDownloadRequest request) {
        // 只有在下载到本地且启用写回功能时才更新
        return Boolean.TRUE.equals(request.getDownloadToLocal()) &&
               Boolean.TRUE.equals(request.getUpdateBitableWithLocalUrl());
    }

    /**
     * 更新多维表格记录，写入本地URL信息
     */
    private void updateBitableRecordWithLocalUrls(String recordId, Map<String, Object> updatedFields,
                                                 FeishuImageDownloadRequest request) {
        try {
            logger.info("开始更新多维表格记录，写入本地URL信息: recordId={}, fields={}", recordId, updatedFields.keySet());

            // 构建更新请求
            FeishuBitableUpdateRequest updateRequest = new FeishuBitableUpdateRequest();
            updateRequest.setAppToken(request.getAppToken());
            updateRequest.setTableId(request.getTableId());
            updateRequest.setRecordId(recordId);
            updateRequest.setFields(updatedFields);

            // 执行更新
            FeishuBitableUpdateResponse updateResponse = updateRecord(updateRequest);

            if (updateResponse.isSuccess()) {
                logger.info("成功更新多维表格记录，写入本地URL信息: recordId={}", recordId);
            } else {
                logger.error("更新多维表格记录失败: recordId={}, error={}", recordId, updateResponse.getMsg());
            }

        } catch (Exception e) {
            logger.error("更新多维表格记录异常: recordId={}", recordId, e);
        }
    }

    /**
     * 判断是否是图片字段
     */
    private boolean isImageField(String fieldName, Object fieldValue, List<String> specifiedImageFields) {
        // 如果指定了图片字段列表，只处理指定的字段
        if (specifiedImageFields != null && !specifiedImageFields.isEmpty()) {
            return specifiedImageFields.contains(fieldName);
        }

        // 否则根据字段值类型判断
        if (fieldValue instanceof List) {
            List<?> list = (List<?>) fieldValue;
            if (!list.isEmpty() && list.get(0) instanceof Map) {
                Map<?, ?> firstItem = (Map<?, ?>) list.get(0);
                // 检查是否包含附件的特征字段
                return firstItem.containsKey("file_token") || firstItem.containsKey("name") || firstItem.containsKey("type");
            }
        }

        // 检查是否是直接的图片URL字符串
        if (fieldValue instanceof String) {
            String urlString = (String) fieldValue;
            return urlString.startsWith("http://") || urlString.startsWith("https://");
        }

        return false;
    }

    /**
     * 处理图片字段
     */
    private List<FeishuImageDownloadResponse.ImageInfo> processImageField(
            Object fieldValue, FeishuImageDownloadRequest request) {

        List<FeishuImageDownloadResponse.ImageInfo> images = new ArrayList<>();

        if (fieldValue instanceof List) {
            List<?> attachments = (List<?>) fieldValue;
            
            for (Object attachment : attachments) {
                if (attachment instanceof Map) {
                    Map<?, ?> attachmentMap = (Map<?, ?>) attachment;
                    FeishuImageDownloadResponse.ImageInfo imageInfo = processSingleImage(attachmentMap, request);
                    images.add(imageInfo);
                }
            }
        }

        return images;
    }

    /**
     * 处理单个图片
     */
    private FeishuImageDownloadResponse.ImageInfo processSingleImage(
            Map<?, ?> attachmentMap, FeishuImageDownloadRequest request) {

        FeishuImageDownloadResponse.ImageInfo imageInfo = new FeishuImageDownloadResponse.ImageInfo();
        
        // 提取基本信息
        imageInfo.setFileToken(getString(attachmentMap, "file_token"));
        imageInfo.setOriginalName(getString(attachmentMap, "name"));
        imageInfo.setFileType(getString(attachmentMap, "type"));
        imageInfo.setFileSize(getLong(attachmentMap, "size"));
        imageInfo.setOriginalUrl(getString(attachmentMap, "url"));
        imageInfo.setTmpDownloadUrl(getString(attachmentMap, "tmp_url"));

        // 如果需要下载到本地
        if (Boolean.TRUE.equals(request.getDownloadToLocal())) {
            downloadImageToLocal(imageInfo, request);
        } else {
            imageInfo.setDownloadStatus("SKIPPED");
        }

        return imageInfo;
    }

    /**
     * 下载图片到本地
     */
    private void downloadImageToLocal(FeishuImageDownloadResponse.ImageInfo imageInfo,
                                    FeishuImageDownloadRequest request) {
        try {
            String downloadUrl = imageInfo.getTmpDownloadUrl();
            if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
                downloadUrl = imageInfo.getOriginalUrl();
            }

            if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
                imageInfo.setDownloadStatus("FAILED");
                imageInfo.setErrorMessage("没有可用的下载URL");
                return;
            }

            logger.info("开始下载图片: {} from URL: {}", imageInfo.getOriginalName(), downloadUrl);

            // 使用现有的图片下载服务下载图片
            String localUrl = downloadImageFromUrl(downloadUrl, imageInfo.getOriginalName());

            if (localUrl != null) {
                imageInfo.setDownloadStatus("SUCCESS");
                imageInfo.setLocalAccessUrl(localUrl);
                imageInfo.setDownloadTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                logger.info("图片下载成功: {} -> {}", imageInfo.getOriginalName(), localUrl);
            } else {
                imageInfo.setDownloadStatus("FAILED");
                imageInfo.setErrorMessage("下载失败，返回的本地URL为空");
                logger.error("图片下载失败: {}", imageInfo.getOriginalName());
            }

        } catch (Exception e) {
            logger.error("下载图片失败: {}", imageInfo.getOriginalName(), e);
            imageInfo.setDownloadStatus("FAILED");
            imageInfo.setErrorMessage(e.getMessage());
        }
    }

    /**
     * 获取真实的下载链接
     */
    private String getRealDownloadUrl(String batchUrl) {
        try {
            logger.debug("调用batch_get_tmp_download_url获取真实下载链接: {}", batchUrl);

            // 获取访问令牌
            String accessToken = tokenService.getAppAccessToken();

            // 创建URL连接
            URL url = new URL(batchUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(feishuConfig.getConnectTimeout());
            connection.setReadTimeout(feishuConfig.getReadTimeout());

            // 设置必要的请求头
            connection.setRequestProperty("Authorization", "Bearer " + accessToken);
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; FeishuImageDownloader/1.0)");

            // 获取响应
            int responseCode = connection.getResponseCode();
            logger.debug("batch_get_tmp_download_url响应状态码: {}", responseCode);

            if (responseCode == 401 || responseCode == 403) {
                logger.warn("batch_get_tmp_download_url认证失败，尝试刷新token");

                // 强制刷新token并重试
                tokenService.forceRefreshAppAccessToken();
                String newAccessToken = tokenService.getAppAccessToken();

                // 重新创建连接
                connection.disconnect();
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(feishuConfig.getConnectTimeout());
                connection.setReadTimeout(feishuConfig.getReadTimeout());
                connection.setRequestProperty("Authorization", "Bearer " + newAccessToken);
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; FeishuImageDownloader/1.0)");

                responseCode = connection.getResponseCode();
                logger.debug("重试后的batch_get_tmp_download_url响应状态码: {}", responseCode);
            }

            if (responseCode != 200) {
                logger.error("batch_get_tmp_download_url请求失败，状态码: {}", responseCode);
                return null;
            }

            // 读取响应
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    connection.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            }

            logger.debug("batch_get_tmp_download_url响应内容: {}", response.toString());

            // 解析JSON响应获取真实下载链接
            Map<String, Object> responseMap = JSON.parseObject(response.toString(), Map.class);

            if (responseMap.containsKey("code") && (Integer) responseMap.get("code") == 0) {
                Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                if (data != null && data.containsKey("tmp_download_urls")) {
                    Object tmpUrlsObj = data.get("tmp_download_urls");

                    // 处理数组格式的响应
                    if (tmpUrlsObj instanceof List) {
                        List<Map<String, Object>> tmpUrlsList = (List<Map<String, Object>>) tmpUrlsObj;
                        if (!tmpUrlsList.isEmpty()) {
                            Map<String, Object> firstItem = tmpUrlsList.get(0);
                            if (firstItem.containsKey("tmp_download_url")) {
                                String downloadUrl = firstItem.get("tmp_download_url").toString();
                                logger.info("成功获取真实下载链接（数组格式）: {}", downloadUrl);
                                return downloadUrl;
                            }
                        }
                    }
                    // 处理对象格式的响应（兼容性）
                    else if (tmpUrlsObj instanceof Map) {
                        Map<String, Object> tmpUrls = (Map<String, Object>) tmpUrlsObj;
                        if (!tmpUrls.isEmpty()) {
                            // 获取第一个文件的下载链接
                            Object firstUrl = tmpUrls.values().iterator().next();
                            if (firstUrl != null) {
                                logger.info("成功获取真实下载链接（对象格式）: {}", firstUrl.toString());
                                return firstUrl.toString();
                            }
                        }
                    }
                }
            }

            logger.error("解析batch_get_tmp_download_url响应失败，未找到下载链接");
            return null;

        } catch (Exception e) {
            logger.error("获取真实下载链接异常", e);
            return null;
        }
    }

    /**
     * 从URL下载图片到本地（带完整token处理）
     */
    private String downloadImageFromUrl(String imageUrl, String originalName) {
        try {
            logger.info("开始下载图片: {} from URL: {}", originalName, imageUrl);

            // 检查URL类型，如果是batch_get_tmp_download_url，需要先获取真实下载链接
            if (imageUrl.contains("batch_get_tmp_download_url")) {
                String realDownloadUrl = getRealDownloadUrl(imageUrl);
                if (realDownloadUrl == null) {
                    logger.error("获取真实下载链接失败: {}", originalName);
                    return null;
                }
                imageUrl = realDownloadUrl;
                logger.info("获取到真实下载链接: {}", imageUrl);
            }

            // 创建URL连接
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(feishuConfig.getConnectTimeout());
            connection.setReadTimeout(feishuConfig.getReadTimeout());

            // 设置必要的请求头
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; FeishuImageDownloader/1.0)");
            connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");

            // 只对飞书域名的URL设置Authorization头
            if (imageUrl.contains("feishu.cn") || imageUrl.contains("larksuite.com")) {
                String accessToken = tokenService.getAppAccessToken();
                connection.setRequestProperty("Authorization", "Bearer " + accessToken);
                logger.debug("为飞书URL设置Authorization头");
            } else {
                logger.debug("外部URL，不设置Authorization头: {}", imageUrl);
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            logger.debug("图片下载响应状态码: {}", responseCode);

            // 只对飞书URL进行token重试
            if ((responseCode == 401 || responseCode == 403) &&
                (imageUrl.contains("feishu.cn") || imageUrl.contains("larksuite.com"))) {
                logger.warn("飞书图片下载认证失败，状态码: {}, 尝试刷新token", responseCode);

                // 强制刷新token并重试
                tokenService.forceRefreshAppAccessToken();
                String newAccessToken = tokenService.getAppAccessToken();

                // 重新创建连接
                connection.disconnect();
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(feishuConfig.getConnectTimeout());
                connection.setReadTimeout(feishuConfig.getReadTimeout());
                connection.setRequestProperty("Authorization", "Bearer " + newAccessToken);
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; FeishuImageDownloader/1.0)");
                connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");

                responseCode = connection.getResponseCode();
                logger.info("重试后的响应状态码: {}", responseCode);
            }

            if (responseCode != 200) {
                logger.error("HTTP请求失败，状态码: {}", responseCode);

                // 读取错误响应
                try (BufferedReader br = new BufferedReader(new InputStreamReader(
                        connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        errorResponse.append(line);
                    }
                    logger.error("错误响应内容: {}", errorResponse.toString());
                }

                return null;
            }

            // 直接处理图片下载和保存，避免重复请求
            return saveImageFromConnection(connection, originalName);

        } catch (Exception e) {
            logger.error("图片下载异常: {}", originalName, e);
            return null;
        }
    }

    /**
     * 从HTTP连接保存图片到本地
     */
    private String saveImageFromConnection(HttpURLConnection connection, String originalName) {
        try {
            // 获取内容类型和文件大小
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLengthLong();

            logger.debug("图片信息 - Content-Type: {}, Content-Length: {}", contentType, contentLength);

            // 验证内容类型
            if (contentType == null || !contentType.startsWith("image/")) {
                logger.error("URL指向的不是图片文件，Content-Type: {}", contentType);
                return null;
            }

            // 生成文件名
            String extension = extractExtensionFromContentType(contentType);
            if (extension == null) {
                extension = extractExtensionFromFileName(originalName);
            }
            if (extension == null) {
                extension = "jpg"; // 默认扩展名
            }

            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String randomId = UUID.randomUUID().toString().substring(0, 8);
            String newFileName = timestamp + "_" + randomId + "." + extension;

            // 创建按日期分组的目录结构
            String dateFolder = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String relativePath = "/uploads/" + dateFolder + "/" + newFileName;
            String fullPath = System.getProperty("user.dir") + "/uploads/" + dateFolder + "/" + newFileName;

            // 确保目录存在
            Path targetPath = Paths.get(fullPath);
            Files.createDirectories(targetPath.getParent());

            // 下载并保存文件
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(targetPath.toFile())) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }

                logger.info("图片保存完成，总大小: {} bytes", totalBytesRead);
            }

            // 生成完整的访问URL
            String fileUrl = generateImageUrl(relativePath);

            logger.info("图片下载并保存成功: {} -> {}", originalName, fileUrl);
            return fileUrl;

        } catch (Exception e) {
            logger.error("保存图片失败: {}", originalName, e);
            return null;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 从Content-Type提取文件扩展名
     */
    private String extractExtensionFromContentType(String contentType) {
        if (contentType == null) return null;

        if (contentType.contains("jpeg") || contentType.contains("jpg")) return "jpg";
        if (contentType.contains("png")) return "png";
        if (contentType.contains("gif")) return "gif";
        if (contentType.contains("bmp")) return "bmp";
        if (contentType.contains("webp")) return "webp";

        return null;
    }

    /**
     * 从文件名提取扩展名
     */
    private String extractExtensionFromFileName(String fileName) {
        if (fileName == null || !fileName.contains(".")) return null;

        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (extension.matches("jpg|jpeg|png|gif|bmp|webp")) {
            return extension;
        }

        return null;
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 安全获取长整型值
     */
    private long getLong(Map<?, ?> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    /**
     * 带token重试机制的执行方法
     */
    private <T> T executeWithTokenRetry(Supplier<T> operation) throws Exception {
        try {
            return operation.get();
        } catch (TokenExpiredException e) {
            logger.warn("Token过期，尝试刷新token并重试: {}", e.getMessage());

            // 强制刷新token
            tokenService.forceRefreshAppAccessToken();
            logger.info("Token已刷新，重试操作");

            // 重试操作
            try {
                return operation.get();
            } catch (RuntimeException re) {
                if (re.getCause() instanceof Exception) {
                    throw (Exception) re.getCause();
                }
                throw re;
            }
        } catch (RuntimeException e) {
            if (e.getCause() instanceof Exception) {
                throw (Exception) e.getCause();
            }
            throw e;
        }
    }

    /**
     * 检查是否是token过期错误
     */
    private boolean isTokenExpiredError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }

        String lowerMsg = errorMessage.toLowerCase();

        // 排除非token相关的错误
        if (lowerMsg.contains("invalidpagetoken") || lowerMsg.contains("1254030")) {
            return false; // InvalidPageToken 不是token过期错误
        }

        // 检查具体的token相关错误码
        return lowerMsg.contains("99991661") || // 飞书缺少token错误码
               lowerMsg.contains("99991663") || // 飞书token过期错误码
               lowerMsg.contains("99991664") || // 飞书token无效错误码
               (lowerMsg.contains("token") && (
                   lowerMsg.contains("expired") ||
                   lowerMsg.contains("unauthorized") ||
                   lowerMsg.contains("missing access token")
               ));
    }

    /**
     * 处理特定的6个图片字段：downImageUrl、downOriginUrl、modelImageUrl、modelMaskImageUrl、upperImageUrl、upperOriginUrl
     * 下载图片到服务器并回写本地URL到多维表格
     *
     * @param request 特定图片字段处理请求参数
     * @return 图片下载响应
     * @throws Exception 处理失败时抛出异常
     */
    public FeishuImageDownloadResponse processSpecificImageFields(FeishuSpecificImageFieldsRequest request) throws Exception {
        logger.info("开始处理特定图片字段: {}", request);
        long startTime = System.currentTimeMillis();

        return executeWithTokenRetry(() -> {
            try {
                return processSpecificImageFieldsInternal(request, startTime);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 内部处理特定图片字段的方法
     */
    private FeishuImageDownloadResponse processSpecificImageFieldsInternal(FeishuSpecificImageFieldsRequest request, long startTime) throws Exception {
        // 转换为通用的图片下载请求
        FeishuImageDownloadRequest downloadRequest = convertToImageDownloadRequest(request);

        // 构建查询请求
        FeishuBitableRequest bitableRequest = buildBitableRequest(downloadRequest);

        // 获取表格记录
        FeishuBitableResponse bitableResponse = getRecordsInternal(bitableRequest, request.getRecordId());

        if (!bitableResponse.isSuccess() || bitableResponse.getData() == null ||
            bitableResponse.getData().getItems() == null) {
            return FeishuImageDownloadResponse.error(400, "获取表格记录失败");
        }

        // 处理图片下载，只处理指定的6个字段
        FeishuImageDownloadResponse.Data responseData = processSpecificImageDownloads(
                bitableResponse.getData().getItems(), request);

        long endTime = System.currentTimeMillis();
        logger.info("特定图片字段处理完成，耗时: {}ms", endTime - startTime);

        return FeishuImageDownloadResponse.success(responseData);
    }

    /**
     * 将特定图片字段请求转换为通用图片下载请求
     */
    private FeishuImageDownloadRequest convertToImageDownloadRequest(FeishuSpecificImageFieldsRequest request) {
        FeishuImageDownloadRequest downloadRequest = new FeishuImageDownloadRequest();
        downloadRequest.setAppToken(request.getAppToken());
        downloadRequest.setTableId(request.getTableId());
        downloadRequest.setRecordId(request.getRecordId());
        downloadRequest.setViewId(request.getViewId());
        downloadRequest.setFilter(request.getFilter());
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageToken(request.getPageToken());
        downloadRequest.setDownloadTimeout(request.getDownloadTimeout());
        downloadRequest.setMaxConcurrentDownloads(request.getMaxConcurrentDownloads());
        downloadRequest.setUpdateBitableWithLocalUrl(request.getUpdateBitableWithLocalUrl());

        // 不指定特定字段名，获取所有字段，然后在处理时筛选
        downloadRequest.setImageFields(null);  // 设置为null表示获取所有字段
        downloadRequest.setDownloadToLocal(true);
        downloadRequest.setIncludeImageDetails(true);

        return downloadRequest;
    }

    /**
     * 处理特定图片字段的下载
     */
    private FeishuImageDownloadResponse.Data processSpecificImageDownloads(
            List<FeishuBitableResponse.Record> records, FeishuSpecificImageFieldsRequest request) {

        FeishuImageDownloadResponse.Data data = new FeishuImageDownloadResponse.Data();
        List<FeishuImageDownloadResponse.RecordImageInfo> recordInfos = new ArrayList<>();

        int totalImages = 0;
        int successfulDownloads = 0;
        int failedDownloads = 0;

        for (FeishuBitableResponse.Record record : records) {
            logger.info("处理记录: {}", record.getRecordId());

            FeishuImageDownloadResponse.RecordImageInfo recordInfo = processSpecificRecordImages(record, request);
            recordInfos.add(recordInfo);

            // 统计图片数量
            for (Map.Entry<String, List<FeishuImageDownloadResponse.ImageInfo>> entry : recordInfo.getImageFields().entrySet()) {
                List<FeishuImageDownloadResponse.ImageInfo> images = entry.getValue();
                totalImages += images.size();
                successfulDownloads += (int) images.stream().filter(img -> "SUCCESS".equals(img.getDownloadStatus())).count();
                failedDownloads += (int) images.stream().filter(img -> "FAILED".equals(img.getDownloadStatus())).count();
            }
        }

        data.setRecords(recordInfos);
        data.setTotalImages(totalImages);
        data.setSuccessfulDownloads(successfulDownloads);
        data.setFailedDownloads(failedDownloads);

        logger.info("特定图片字段处理完成 - 总图片数: {}, 成功: {}, 失败: {}",
                totalImages, successfulDownloads, failedDownloads);

        return data;
    }

    /**
     * 处理单个记录的特定图片字段
     */
    private FeishuImageDownloadResponse.RecordImageInfo processSpecificRecordImages(
            FeishuBitableResponse.Record record, FeishuSpecificImageFieldsRequest request) {

        FeishuImageDownloadResponse.RecordImageInfo recordInfo = new FeishuImageDownloadResponse.RecordImageInfo();
        recordInfo.setRecordId(record.getRecordId());

        Map<String, List<FeishuImageDownloadResponse.ImageInfo>> imageFields = new HashMap<>();
        Map<String, Object> updatedFields = new HashMap<>();

        // 只处理指定的6个图片字段
        List<String> specificFields = request.getSpecificImageFields();

        if (record.getFields() != null) {
            for (String fieldName : specificFields) {
                Object fieldValue = record.getFields().get(fieldName);

                if (fieldValue != null) {
                    logger.debug("检查字段: {} = {} (类型: {})", fieldName, fieldValue, fieldValue.getClass().getSimpleName());

                    // 检查是否是我们要处理的特定字段，并且包含图片数据
                    if (specificFields.contains(fieldName) && isImageField(fieldName, fieldValue, null)) {
                        logger.info("处理图片字段: {} for record: {}", fieldName, record.getRecordId());

                        List<FeishuImageDownloadResponse.ImageInfo> images;

                        // 如果是直接的URL字符串，使用专门的处理方法
                        if (fieldValue instanceof String) {
                            images = processUrlStringField((String) fieldValue, fieldName);
                        } else {
                            // 转换为通用请求进行处理（处理飞书附件格式）
                            FeishuImageDownloadRequest downloadRequest = convertToImageDownloadRequest(request);
                            images = processImageField(fieldValue, downloadRequest);
                        }

                        imageFields.put(fieldName, images);

                    // 如果有成功下载的图片，准备更新字段
                    for (FeishuImageDownloadResponse.ImageInfo imageInfo : images) {
                        // 如果下载成功，准备回写数据
                        if ("SUCCESS".equals(imageInfo.getDownloadStatus()) && imageInfo.getLocalAccessUrl() != null) {

                            // 对于URL字符串字段，直接回写本地URL
                            if (fieldValue instanceof String) {
                                updatedFields.put(fieldName, imageInfo.getLocalAccessUrl());
                                logger.info("准备回写URL字段 {}: {} -> {}", fieldName, fieldValue, imageInfo.getLocalAccessUrl());
                            } else {
                                // 对于附件字段，将本地URL写回到对应的URL字段（去掉"附件"后缀）
                                String targetFieldName = fieldName.replace("附件", "");
                                updatedFields.put(targetFieldName, imageInfo.getLocalAccessUrl());
                                logger.info("准备回写URL字段 {} (从附件字段 {}): {}", targetFieldName, fieldName, imageInfo.getLocalAccessUrl());
                            }
                        }
                    }
                    } else {
                        logger.debug("字段 {} 不是图片字段或为空", fieldName);
                    }
                }
            }
        }

        recordInfo.setImageFields(imageFields);

        // 如果需要回写且有更新的字段，执行回写操作
        if (!updatedFields.isEmpty() && request.getUpdateBitableWithLocalUrl()) {
            logger.info("开始回写本地URL到特定图片字段: recordId={}, fieldsToUpdate={}",
                    record.getRecordId(), updatedFields.keySet());
            updateSpecificImageFieldsWithLocalUrls(record.getRecordId(), updatedFields, request);
        }

        return recordInfo;
    }

    /**
     * 更新特定图片字段的本地URL信息
     */
    private void updateSpecificImageFieldsWithLocalUrls(String recordId, Map<String, Object> updatedFields,
                                                       FeishuSpecificImageFieldsRequest request) {
        try {
            logger.info("开始更新特定图片字段的本地URL信息: recordId={}, fields={}", recordId, updatedFields.keySet());

            // 构建更新请求
            FeishuBitableUpdateRequest updateRequest = new FeishuBitableUpdateRequest();
            updateRequest.setAppToken(request.getAppToken());
            updateRequest.setTableId(request.getTableId());
            updateRequest.setRecordId(recordId);
            updateRequest.setFields(updatedFields);

            // 执行更新
            FeishuBitableUpdateResponse updateResponse = updateRecord(updateRequest);

            if (updateResponse.isSuccess()) {
                logger.info("特定图片字段本地URL写回成功: recordId={}, updatedFields={}",
                        recordId, updatedFields.keySet());
            } else {
                logger.error("特定图片字段本地URL写回失败: recordId={}, error={}",
                        recordId, updateResponse.getMsg());
            }

        } catch (Exception e) {
            logger.error("更新特定图片字段本地URL异常: recordId=" + recordId, e);
        }
    }

    /**
     * 处理URL字符串字段，直接从URL下载图片
     */
    private List<FeishuImageDownloadResponse.ImageInfo> processUrlStringField(String imageUrl, String fieldName) {
        List<FeishuImageDownloadResponse.ImageInfo> images = new ArrayList<>();

        FeishuImageDownloadResponse.ImageInfo imageInfo = new FeishuImageDownloadResponse.ImageInfo();

        // 设置基本信息
        imageInfo.setOriginalUrl(imageUrl);
        imageInfo.setTmpDownloadUrl(imageUrl);

        // 从URL中提取文件名
        String fileName = extractFileNameFromUrl(imageUrl);
        imageInfo.setOriginalName(fileName);

        // 设置文件类型
        String fileType = getFileTypeFromUrl(imageUrl);
        imageInfo.setFileType(fileType);

        logger.info("开始从URL下载图片: {} (字段: {})", imageUrl, fieldName);

        // 使用现有的图片下载服务下载图片
        String localUrl = downloadImageFromUrl(imageUrl, fileName);

        if (localUrl != null) {
            imageInfo.setDownloadStatus("SUCCESS");
            imageInfo.setLocalAccessUrl(localUrl);
            imageInfo.setDownloadTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            logger.info("URL字段图片下载成功: {} -> {}", fileName, localUrl);
        } else {
            imageInfo.setDownloadStatus("FAILED");
            imageInfo.setErrorMessage("从URL下载失败: " + imageUrl);
            logger.error("URL字段图片下载失败: {} from URL: {}", fileName, imageUrl);
        }

        images.add(imageInfo);
        return images;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            String urlPath = url.split("\\?")[0]; // 移除查询参数
            int lastSlashIndex = urlPath.lastIndexOf("/");
            if (lastSlashIndex != -1 && lastSlashIndex < urlPath.length() - 1) {
                return urlPath.substring(lastSlashIndex + 1);
            }
        } catch (Exception e) {
            logger.warn("无法从URL提取文件名: " + url, e);
        }
        return "downloaded_image";
    }

    /**
     * 从URL中推断文件类型
     */
    private String getFileTypeFromUrl(String url) {
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.contains(".jpg") || lowerUrl.contains(".jpeg")) {
            return "image/jpeg";
        } else if (lowerUrl.contains(".png")) {
            return "image/png";
        } else if (lowerUrl.contains(".gif")) {
            return "image/gif";
        } else if (lowerUrl.contains(".webp")) {
            return "image/webp";
        } else if (lowerUrl.contains(".bmp")) {
            return "image/bmp";
        }
        return "image/jpeg"; // 默认类型
    }

    /**
     * 生成图片访问URL（支持代理模式）
     */
    private String generateImageUrl(String relativePath) {
        logger.info("=== 生成图片URL开始 ===");
        logger.info("relativePath: {}", relativePath);
        logger.info("imageProxyEnabled: {}", imageProxyEnabled);
        logger.info("imageProxyService != null: {}", imageProxyService != null);
        logger.info("isImageFile: {}", isImageFile(relativePath));

        // 如果启用了图片代理且服务可用，使用代理URL
        if (imageProxyEnabled && imageProxyService != null && isImageFile(relativePath)) {
            logger.info("条件满足，开始生成代理URL: {}", relativePath);
            // 由于没有HttpServletRequest，使用简化的代理URL生成
            String proxyUrl = generateSimpleProxyUrl(relativePath);
            if (proxyUrl != null) {
                logger.info("代理URL生成成功: {} -> {}", relativePath, proxyUrl);
                return proxyUrl;
            }
            logger.warn("生成代理URL失败，回退到直接URL: {}", relativePath);
        } else {
            logger.info("条件不满足，使用直接URL方式");
        }

        // 使用传统的直接URL方式
        String directUrl = serverBaseUrl + relativePath;
        logger.info("直接URL: {}", directUrl);
        logger.info("=== 生成图片URL结束 ===");
        return directUrl;
    }

    /**
     * 生成简化的代理URL（不依赖HttpServletRequest）
     */
    private String generateSimpleProxyUrl(String relativePath) {
        try {
            logger.info("=== 生成简化代理URL开始 ===");
            logger.info("输入relativePath: {}", relativePath);

            // 标准化文件路径
            String normalizedPath = normalizeFilePath(relativePath);
            logger.info("标准化后路径: {}", normalizedPath);

            // 生成图片ID
            String imageId = generateImageId(normalizedPath);
            logger.info("生成的图片ID: {}", imageId);

            // 如果ImageProxyService可用，注册映射关系
            if (imageProxyService != null) {
                imageProxyService.registerImageMapping(imageId, normalizedPath);
                logger.info("已注册ID映射: {} -> {}", imageId, normalizedPath);
            } else {
                logger.warn("ImageProxyService为null，无法注册映射");
            }

            String proxyUrl = serverBaseUrl + "/api/image-proxy/id/" + imageId;
            logger.info("生成的代理URL: {}", proxyUrl);
            logger.info("=== 生成简化代理URL结束 ===");
            return proxyUrl;

        } catch (Exception e) {
            logger.error("生成简化代理URL失败: " + relativePath, e);
            return null;
        }
    }

    /**
     * 标准化文件路径
     */
    private String normalizeFilePath(String filePath) {
        // 移除服务器基础URL
        if (filePath.startsWith(serverBaseUrl)) {
            filePath = filePath.substring(serverBaseUrl.length());
        }

        // 确保以/开头
        if (!filePath.startsWith("/")) {
            filePath = "/" + filePath;
        }

        return filePath;
    }

    /**
     * 生成图片ID
     */
    private String generateImageId(String imagePath) {
        try {
            // 使用简单的哈希算法生成ID
            int hash = Math.abs((imagePath + "ziniao-image-proxy-2025").hashCode());
            String hexString = String.format("%08x", hash);

            // 如果字符串长度不足12位，重复拼接
            while (hexString.length() < 12) {
                hexString += String.format("%08x", Math.abs((hexString + imagePath).hashCode()));
            }

            // 截取前12位
            return hexString.substring(0, 12);

        } catch (Exception e) {
            logger.error("生成图片ID失败", e);
            return String.valueOf(Math.abs(imagePath.hashCode()));
        }
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }

        String extension = getFileExtension(filePath).toLowerCase();
        return Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg").contains(extension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 处理字段映射图片上传
     * 根据字段映射关系，从源图片字段下载图片并将本地URL写入目标字段
     *
     * @param request 字段映射图片上传请求参数
     * @return 图片下载响应
     * @throws Exception 处理失败时抛出异常
     */
    public FeishuImageDownloadResponse processFieldMappingImages(FeishuFieldMappingImageRequest request) throws Exception {
        logger.info("开始处理字段映射图片上传: {}", request);
        long startTime = System.currentTimeMillis();

        return executeWithTokenRetry(() -> {
            try {
                return processFieldMappingImagesInternal(request, startTime);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 内部字段映射图片处理方法
     */
    private FeishuImageDownloadResponse processFieldMappingImagesInternal(FeishuFieldMappingImageRequest request, long startTime) throws Exception {
        // 转换为通用的图片下载请求
        FeishuImageDownloadRequest downloadRequest = convertFieldMappingToImageDownloadRequest(request);

        // 构建查询请求
        FeishuBitableRequest bitableRequest = buildBitableRequest(downloadRequest);

        // 获取表格记录
        FeishuBitableResponse bitableResponse = getRecordsInternal(bitableRequest, request.getRecordId());

        if (!bitableResponse.isSuccess() || bitableResponse.getData() == null ||
            bitableResponse.getData().getItems() == null) {
            return FeishuImageDownloadResponse.error(400, "获取表格记录失败");
        }

        // 处理字段映射图片下载
        FeishuImageDownloadResponse.Data responseData = processFieldMappingImageDownloads(
                bitableResponse.getData().getItems(), request);

        responseData.setProcessingTime(System.currentTimeMillis() - startTime);
        responseData.setHasMore(bitableResponse.getData().isHasMore());
        responseData.setNextPageToken(bitableResponse.getData().getPageToken());

        return FeishuImageDownloadResponse.success(responseData);
    }

    /**
     * 转换字段映射请求为通用图片下载请求
     */
    private FeishuImageDownloadRequest convertFieldMappingToImageDownloadRequest(FeishuFieldMappingImageRequest request) {
        FeishuImageDownloadRequest downloadRequest = new FeishuImageDownloadRequest();
        downloadRequest.setAppToken(request.getAppToken());
        downloadRequest.setTableId(request.getTableId());
        downloadRequest.setRecordId(request.getRecordId());
        downloadRequest.setViewId(request.getViewId());
        downloadRequest.setFilter(request.getFilter());
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageToken(request.getPageToken());
        downloadRequest.setDownloadTimeout(request.getDownloadTimeout());
        downloadRequest.setMaxConcurrentDownloads(request.getMaxConcurrentDownloads());
        downloadRequest.setUpdateBitableWithLocalUrl(request.getUpdateBitableWithLocalUrl());
        downloadRequest.setIncludeImageDetails(request.getIncludeImageDetails());

        // 不设置特定的图片字段，获取所有字段，在处理时再过滤
        // 这样避免了字段名称在API请求中的编码问题
        downloadRequest.setImageFields(null);
        downloadRequest.setDownloadToLocal(true);

        return downloadRequest;
    }

    /**
     * 处理字段映射图片下载
     */
    private FeishuImageDownloadResponse.Data processFieldMappingImageDownloads(
            List<FeishuBitableResponse.Record> records, FeishuFieldMappingImageRequest request) {

        FeishuImageDownloadResponse.Data data = new FeishuImageDownloadResponse.Data();
        List<FeishuImageDownloadResponse.RecordImageInfo> recordInfos = new ArrayList<>();

        int totalImages = 0;
        int successfulDownloads = 0;
        int failedDownloads = 0;

        for (FeishuBitableResponse.Record record : records) {
            FeishuImageDownloadResponse.RecordImageInfo recordInfo = processFieldMappingRecordImages(record, request);
            recordInfos.add(recordInfo);

            // 统计图片数量
            for (List<FeishuImageDownloadResponse.ImageInfo> images : recordInfo.getImageFields().values()) {
                totalImages += images.size();
                successfulDownloads += (int) images.stream().filter(img -> "SUCCESS".equals(img.getDownloadStatus())).count();
                failedDownloads += (int) images.stream().filter(img -> "FAILED".equals(img.getDownloadStatus())).count();
            }
        }

        data.setTotalRecords(records.size());
        data.setTotalImages(totalImages);
        data.setSuccessfulDownloads(successfulDownloads);
        data.setFailedDownloads(failedDownloads);
        data.setRecords(recordInfos);

        return data;
    }

    /**
     * 处理单个记录的字段映射图片
     */
    private FeishuImageDownloadResponse.RecordImageInfo processFieldMappingRecordImages(
            FeishuBitableResponse.Record record, FeishuFieldMappingImageRequest request) {

        FeishuImageDownloadResponse.RecordImageInfo recordInfo = new FeishuImageDownloadResponse.RecordImageInfo();
        recordInfo.setRecordId(record.getRecordId());

        Map<String, List<FeishuImageDownloadResponse.ImageInfo>> imageFields = new HashMap<>();
        Map<String, Object> updatedFields = new HashMap<>();

        // 获取字段映射关系
        Map<String, String> fieldMapping = request.getFieldMapping();

        logger.info("处理记录 {} 的字段映射图片，映射关系: {}", record.getRecordId(), fieldMapping);

        // 遍历记录的所有字段
        if (record.getFields() != null) {
            for (Map.Entry<String, Object> fieldEntry : record.getFields().entrySet()) {
                String fieldName = fieldEntry.getKey();
                Object fieldValue = fieldEntry.getValue();

                // 检查是否是映射关系中的源字段
                if (fieldMapping.containsKey(fieldName) && fieldValue != null) {
                    String targetFieldName = fieldMapping.get(fieldName);
                    logger.info("处理字段映射: {} -> {}", fieldName, targetFieldName);

                    // 检查是否是图片字段
                    if (isImageField(fieldName, fieldValue, null)) {
                        logger.info("处理图片字段: {} for record: {}", fieldName, record.getRecordId());

                        List<FeishuImageDownloadResponse.ImageInfo> images;

                        // 如果是直接的URL字符串，使用专门的处理方法
                        if (fieldValue instanceof String) {
                            images = processUrlStringField((String) fieldValue, fieldName);
                        } else {
                            // 转换为通用请求进行处理（处理飞书附件格式）
                            FeishuImageDownloadRequest downloadRequest = convertFieldMappingToImageDownloadRequest(request);
                            images = processImageField(fieldValue, downloadRequest);
                        }

                        imageFields.put(fieldName, images);

                        // 如果有成功下载的图片，准备更新目标字段
                        for (FeishuImageDownloadResponse.ImageInfo imageInfo : images) {
                            if ("SUCCESS".equals(imageInfo.getDownloadStatus()) && imageInfo.getLocalAccessUrl() != null) {
                                // 将本地URL写入目标字段
                                updatedFields.put(targetFieldName, imageInfo.getLocalAccessUrl());
                                logger.info("准备回写URL字段 {} (从源字段 {}): {}",
                                           targetFieldName, fieldName, imageInfo.getLocalAccessUrl());
                                break; // 只取第一个成功的图片URL
                            }
                        }
                    } else {
                        logger.debug("字段 {} 不是图片字段或为空", fieldName);
                    }
                }
            }
        }

        recordInfo.setImageFields(imageFields);

        // 如果需要回写且有更新的字段，执行回写操作
        if (!updatedFields.isEmpty() && request.getUpdateBitableWithLocalUrl()) {
            logger.info("开始回写本地URL到字段映射目标字段: recordId={}, fieldsToUpdate={}",
                    record.getRecordId(), updatedFields.keySet());
            updateFieldMappingWithLocalUrls(record.getRecordId(), updatedFields, request);
        }

        return recordInfo;
    }

    /**
     * 更新字段映射的本地URL信息
     */
    private void updateFieldMappingWithLocalUrls(String recordId, Map<String, Object> updatedFields,
                                                FeishuFieldMappingImageRequest request) {
        try {
            logger.info("开始更新字段映射的本地URL信息: recordId={}, fields={}", recordId, updatedFields.keySet());

            // 构建更新请求
            FeishuBitableUpdateRequest updateRequest = new FeishuBitableUpdateRequest();
            updateRequest.setAppToken(request.getAppToken());
            updateRequest.setTableId(request.getTableId());
            updateRequest.setRecordId(recordId);
            updateRequest.setFields(updatedFields);

            // 执行更新
            FeishuBitableUpdateResponse updateResponse = updateRecord(updateRequest);

            if (updateResponse.isSuccess()) {
                logger.info("字段映射本地URL信息更新成功: recordId={}, updatedFields={}",
                           recordId, updatedFields.keySet());
            } else {
                logger.error("字段映射本地URL信息更新失败: recordId={}, error={}",
                            recordId, updateResponse.getMsg());
            }

        } catch (Exception e) {
            logger.error("更新字段映射本地URL信息异常: recordId=" + recordId, e);
        }
    }

}
